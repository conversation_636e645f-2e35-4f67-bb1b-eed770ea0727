<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <title>Contact Tutors Alliance Scotland</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/responsive-helper.js"></script>
    <script src="/js/nav-loader.js" defer=""></script>
    <script src="/js/dynamic-nav.js" defer=""></script>
    <script type="module" src="/js/visual-editor.js?v=20250101" defer=""></script>
    <style>
        /* Public form container styling */
        .public-form-container {
            max-width: 700px;
            margin: 40px auto;
            background-color: #C8A2C8; /* Lilac background */
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

            .public-form-container h3 {
                margin-bottom: 20px;
                color: #333;
            }

        .public-submission-form label {
            display: block;
            margin-top: 15px;
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
        }

        .public-submission-form input,
        .public-submission-form select,
        .public-submission-form textarea {
            width: 100%;
            padding: 10px;
            margin-top: 5px;
            font-size: 1em;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .public-submission-form textarea {
            min-height: 120px;
            resize: vertical;
        }

        .public-submission-form button {
            margin-top: 20px;
            padding: 12px 20px;
            font-size: 1.1em;
            background-color: #0057B7;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease-in-out;
        }

            .public-submission-form button:hover {
                background-color: #0046a5;
                box-shadow: 0 0 10px #C8A2C8;
            }

            .public-submission-form button:active {
                background-color: #C8A2C8;
                color: #fff;
            }
    </style>
</head>
<body data-page="publicConnect">
    <!-- Shared banner/header -->
    <header>
        <h1 data-ve-block-id="02e86e68-7285-4c39-97e2-769a3bbd8c64">Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="/" class="banner-login-link login-box" data-ve-block-id="fdc72013-4afb-48ca-b742-ecd1da0348c2">Home</a>
            <a href="login.html?role=admin" class="banner-login-link login-box" data-ve-block-id="484dad10-0f9e-44a7-a755-3f6dec2aed9a">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS will populate tutor names/subjects here -->
        </div>
    </div>

    <main>
        <h2 data-ve-block-id="f87a18ff-8fa9-41d0-9148-142ed3974be0">Get in Touch</h2>
        <p data-ve-block-id="7a512df0-c69b-4617-8fab-d4c7a6226a3c">
            We'd love to hear from you! Whether you have questions about our services, need help finding the right educational support,
            or want to learn more about what we do, please fill out the form below and we'll get back to you as soon as possible.
        </p>

        <!-- TOP dynamic sections container -->
        <section id="dynamicSectionsTop" class="dynamic-section-container"></section>

        <div class="public-form-container">
            <h3 data-ve-block-id="b4c37955-4b0c-457b-a836-6533d3db98b0">Contact Form</h3>
            <form id="publicSubmissionForm" class="public-submission-form">
                <label for="publicName">Full Name:</label>
                <input type="text" id="publicName" name="publicName" required="">

                <label for="publicEmail">Email Address:</label>
                <input type="email" id="publicEmail" name="publicEmail" required="">

                <label for="publicPhone">Phone Number (Optional):</label>
                <input type="tel" id="publicPhone" name="publicPhone">

                <label for="publicSubject">Subject:</label>
                <select id="publicSubject" name="publicSubject" required="">
                    <option value="">Please select...</option>
                    <option value="General Inquiry">General Inquiry</option>
                    <option value="Finding a Tutor">Finding a Tutor</option>
                    <option value="Partnership Opportunities">Partnership Opportunities</option>
                    <option value="Feedback">Feedback</option>
                    <option value="Other">Other</option>
                </select>

                <label for="publicMessage">Message:</label>
                <textarea id="publicMessage" name="publicMessage" placeholder="Please tell us how we can help you..." required=""></textarea>

                <button type="submit">Send Message</button>
            </form>
        </div>

        <!-- MIDDLE dynamic sections container -->
        <section id="dynamicSectionsMiddle" class="dynamic-section-container"></section>

        <!-- Clear separator before dynamic sections -->
        <div class="dynamic-sections-separator"></div>

        <!-- BOTTOM dynamic sections container -->
        <section id="dynamicSections" class="dynamic-section-container"></section>
    </main>

    <script src="/js/rolling-banner.js"></script>
    <script>
        // Add a status message element
        const statusMessageDiv = document.createElement('div');
        statusMessageDiv.id = 'statusMessage';
        statusMessageDiv.style.marginTop = '20px';
        statusMessageDiv.style.padding = '10px';
        statusMessageDiv.style.borderRadius = '5px';
        statusMessageDiv.style.display = 'none';
        document.querySelector('.public-form-container').appendChild(statusMessageDiv);

        // Function to show status messages
        function showStatusMessage(message, isError = false) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';

            if (isError) {
                statusDiv.style.backgroundColor = '#ffdddd';
                statusDiv.style.color = '#d8000c';
                statusDiv.style.border = '1px solid #d8000c';
            } else {
                statusDiv.style.backgroundColor = '#dff2bf';
                statusDiv.style.color = '#4f8a10';
                statusDiv.style.border = '1px solid #4f8a10';
            }

            // Scroll to the message
            statusDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        document.getElementById('publicSubmissionForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            // Show loading message
            showStatusMessage('Sending your message... Please wait.', false);

            // Disable the submit button to prevent multiple submissions
            const submitButton = e.target.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';

            const formData = {
                name: document.getElementById('publicName').value.trim(),
                email: document.getElementById('publicEmail').value.trim(),
                phone: document.getElementById('publicPhone').value.trim(),
                subject: document.getElementById('publicSubject').value.trim(),
                message: document.getElementById('publicMessage').value.trim()
            };

            try {
                // POST to /api/connection (unified API)
                const response = await fetch('/api/connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });

                // Check if the response is JSON
                let data;
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                } else {
                    // If not JSON, get the text and create an error
                    const text = await response.text();
                    console.error('Non-JSON response:', text);
                    throw new Error('Server returned an invalid response format. Please try again later.');
                }

                if (!response.ok) {
                    throw new Error(data.message || data.error || 'Failed to send message');
                }

                // Show success message
                showStatusMessage("Thank you for your message! We will get back to you soon.", false);
                document.getElementById('publicSubmissionForm').reset();
            } catch (err) {
                console.error("Error submitting message:", err);
                showStatusMessage(`Message failed to send: ${err.message}. Please try again.`, true);
            } finally {
                // Re-enable the submit button
                submitButton.disabled = false;
                submitButton.textContent = 'Send Message';
            }
        });
    </script>

<!-- Visual Editor Templates -->
<template id="ve-editor-modal-template">
    <div id="editor-modal" class="ve-modal-container">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Edit Content</h3>
                <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
            </div>
            <div class="modal-body">
                <form id="content-form" onsubmit="return false;">
                    <div class="form-group">
                        <label for="content-type">Content Type:</label>
                        <select id="content-type" disabled="">
                            <option value="text">Text</option>
                            <option value="html">HTML</option>
                            <option value="image">Image</option>
                            <option value="link">Link</option>
                        </select>
                    </div>
                    <div class="form-group" id="text-group">
                        <label for="content-text">Text Content:</label>
                        <textarea id="content-text" rows="4"></textarea>
                    </div>
                    <div class="form-group" id="html-group">
                        <label for="content-html">HTML Content:</label>
                        <textarea id="content-html" rows="6"></textarea>
                    </div>
                    <div class="form-group" id="image-group">
                        <label for="content-image">Image URL:</label>
                        <div class="image-input-group">
                            <input type="url" id="content-image">
                            <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                        </div>
                        <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                        <div class="upload-section">
                            <label for="image-upload">Or upload a new image:</label>
                            <input type="file" id="image-upload" accept="image/*">
                            <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                            <div id="upload-progress" style="display: none;">
                                <div class="progress-bar"><div class="progress-fill"></div></div>
                                <span class="progress-text">Uploading...</span>
                            </div>
                        </div>
                        <label for="image-alt">Alt Text:</label>
                        <input type="text" id="image-alt">
                    </div>
                    <div class="form-group" id="link-group">
                        <label for="link-url">Link URL:</label>
                        <input type="url" id="link-url">
                        <label for="link-text">Link Text:</label>
                        <input type="text" id="link-text">
                        <div class="form-check mt-2">
                            <input type="checkbox" id="link-is-button" class="form-check-input">
                            <label for="link-is-button" class="form-check-label">Style as button</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                        <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                        <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                    </div>
                </form>
            </div>
        </div>
        <div id="image-browser" class="image-browser" style="display: none;"></div>
    </div>
</template>

<template id="ve-image-browser-template">
    <div class="image-browser-header">
        <h4>Browse Images</h4>
        <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
    </div>
    <div class="image-browser-content">
        <div class="image-browser-toolbar">
            <input type="text" id="image-search" placeholder="Search images..." class="form-control">
            <select id="image-sort" class="form-control">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name</option>
            </select>
        </div>
        <div id="image-grid" class="image-grid">
            <div class="loading-spinner"></div>
        </div>
        <div id="image-pagination" class="image-pagination">
            <button type="button" id="prev-page" class="btn btn-secondary" disabled="">Previous</button>
            <span id="page-info">Page 1</span>
            <button type="button" id="next-page" class="btn btn-secondary">Next</button>
        </div>
    </div>
</template>


</body></html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Persistence Fix</title>
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/editor.css">
    <script src="/test-fix-verification.js"></script>
    <script type="module" src="/js/visual-editor.js?v=20250101-fix2&cache=bust" defer=""></script>
    <style>
        .test-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 8px;
        }
        .test-image {
            max-width: 300px;
            height: auto;
            border-radius: 8px;
        }
        .debug-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-good { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-error { background: #F44336; }
    </style>
</head>
<body data-page="test-image-persistence">
    <div class="test-container">
        <h1>🔧 Image Persistence Fix Test</h1>
        
        <div class="debug-info">
            <h3>🐛 Debug Status:</h3>
            <div id="debug-status">
                <div><span class="status-indicator status-warning"></span>Waiting for visual editor to load...</div>
            </div>
        </div>

        <div class="test-section" data-ve-section-id="test-section">
            <h2 data-ve-block-id="test-heading">Test Image Section</h2>
            <p data-ve-block-id="test-description">This section contains a test image that you can edit to verify the persistence fix is working.</p>
            
            <div data-ve-block-id="test-image-container">
                <img src="/images/centralShield.png" alt="Test Image" class="test-image" data-ve-block-id="test-image">
            </div>
            
            <p data-ve-block-id="test-instructions">
                <strong>Instructions:</strong><br>
                1. Log in as admin<br>
                2. Toggle edit mode (Ctrl+E)<br>
                3. Click on the image above<br>
                4. Upload a new image<br>
                5. Save the changes<br>
                6. Open this page in a different browser/device<br>
                7. Check if your new image persists
            </p>
        </div>

        <div class="debug-info">
            <h3>📊 Override Application Log:</h3>
            <div id="override-log" style="max-height: 200px; overflow-y: auto;">
                <div>Waiting for override applications...</div>
            </div>
        </div>

        <div class="debug-info">
            <h3>🔄 Cache Information:</h3>
            <div id="cache-info">
                <div>Visual Editor Version: <span id="ve-version">Loading...</span></div>
                <div>Page Load Time: <span id="load-time"></span></div>
                <div>Browser: <span id="browser-info"></span></div>
            </div>
        </div>
    </div>

    <script>
        // Debug logging and status tracking
        let overrideCount = 0;
        let debugLog = [];
        
        // Capture console logs related to visual editor
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const message = args.join(' ');
            if (message.includes('[VE]') || message.includes('override') || message.includes('image')) {
                debugLog.push({
                    time: new Date().toLocaleTimeString(),
                    message: message
                });
                updateOverrideLog();
                
                if (message.includes('applyOverride → image')) {
                    overrideCount++;
                    updateDebugStatus();
                }
            }
        };

        function updateDebugStatus() {
            const statusDiv = document.getElementById('debug-status');
            let status = '';
            
            if (overrideCount === 0) {
                status = '<div><span class="status-indicator status-warning"></span>No image overrides detected yet</div>';
            } else if (overrideCount === 1) {
                status = '<div><span class="status-indicator status-good"></span>✅ Single image override applied (GOOD)</div>';
            } else {
                status = `<div><span class="status-indicator status-error"></span>⚠️ Multiple image overrides detected (${overrideCount}) - Loop detected!</div>`;
            }
            
            statusDiv.innerHTML = status;
        }

        function updateOverrideLog() {
            const logDiv = document.getElementById('override-log');
            const recentLogs = debugLog.slice(-10); // Show last 10 logs
            
            logDiv.innerHTML = recentLogs.map(log => 
                `<div><strong>${log.time}:</strong> ${log.message}</div>`
            ).join('');
            
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Initialize page info
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('load-time').textContent = new Date().toLocaleTimeString();
            document.getElementById('browser-info').textContent = navigator.userAgent.split(' ')[0];
            
            // Check if visual editor loaded with version
            const veScript = document.querySelector('script[src*="visual-editor.js"]');
            if (veScript) {
                const version = veScript.src.match(/v=([^&]*)/);
                document.getElementById('ve-version').textContent = version ? version[1] : 'No version';
            }
            
            // Listen for visual editor events
            window.addEventListener('ve-overrides-done', function() {
                debugLog.push({
                    time: new Date().toLocaleTimeString(),
                    message: '🎉 ve-overrides-done event received'
                });
                updateOverrideLog();
            });
        });

        // Reset counter on page navigation
        window.addEventListener('beforeunload', function() {
            overrideCount = 0;
        });
    </script>
</body>
</html>
